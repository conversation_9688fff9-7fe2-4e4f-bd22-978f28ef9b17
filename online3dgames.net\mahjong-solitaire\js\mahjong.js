/**
 * Mahjong Solitaire - Tile System and Data Structures
 */

class MahjongTile {
    constructor(type, value, unicode, id) {
        this.type = type;           // 'wan', 'tong', 'tiao', 'feng', 'jian', 'hua', 'season'
        this.value = value;         // 1-9 for numbered tiles, specific values for others
        this.unicode = unicode;     // Unicode character for display
        this.id = id;              // Unique identifier
        this.x = 0;                // Position X
        this.y = 0;                // Position Y
        this.layer = 0;            // Layer (Z-index)
        this.element = null;       // DOM element
        this.selectable = false;   // Can be selected
        this.selected = false;     // Currently selected
        this.removed = false;      // Has been removed
    }

    /**
     * Check if this tile can match with another tile
     */
    canMatch(otherTile) {
        if (!otherTile || this.removed || otherTile.removed) {
            return false;
        }

        // Exact match for most tiles
        if (this.type === otherTile.type && this.value === otherTile.value) {
            return true;
        }

        // Special matching rules for flower and season tiles
        if (this.type === 'hua' && otherTile.type === 'hua') {
            return true; // Any flower matches any flower
        }
        
        if (this.type === 'season' && otherTile.type === 'season') {
            return true; // Any season matches any season
        }

        return false;
    }

    /**
     * Get CSS classes for this tile
     */
    getCSSClasses() {
        const classes = ['mahjong-tile', `tile-${this.type}`];
        
        if (this.selected) classes.push('selected');
        if (!this.selectable) classes.push('blocked');
        if (this.removed) classes.push('removed');
        
        return classes.join(' ');
    }

    /**
     * Create DOM element for this tile
     */
    createElement() {
        if (this.element) {
            return this.element;
        }

        const element = document.createElement('div');
        element.className = this.getCSSClasses();
        element.textContent = this.unicode;
        element.dataset.tileId = this.id;
        element.dataset.type = this.type;
        element.dataset.value = this.value;
        element.dataset.layer = this.layer;
        
        // Position the element
        element.style.left = `${this.x}px`;
        element.style.top = `${this.y}px`;
        element.style.zIndex = this.layer * 10 + 1;

        this.element = element;
        return element;
    }

    /**
     * Update tile position
     */
    setPosition(x, y, layer = this.layer) {
        this.x = x;
        this.y = y;
        this.layer = layer;
        
        if (this.element) {
            this.element.style.left = `${x}px`;
            this.element.style.top = `${y}px`;
            this.element.style.zIndex = layer * 10 + 1;
            this.element.dataset.layer = layer;
        }
    }

    /**
     * Update tile selectability
     */
    setSelectable(selectable) {
        this.selectable = selectable;
        if (this.element) {
            this.element.className = this.getCSSClasses();
        }
    }

    /**
     * Select/deselect tile
     */
    setSelected(selected) {
        this.selected = selected;
        if (this.element) {
            this.element.className = this.getCSSClasses();
        }
    }

    /**
     * Remove tile from game
     */
    remove() {
        this.removed = true;
        this.selected = false;
        this.selectable = false;
        
        if (this.element) {
            this.element.classList.add('removing');
            setTimeout(() => {
                if (this.element && this.element.parentNode) {
                    this.element.parentNode.removeChild(this.element);
                }
            }, 500);
        }
    }
}

class MahjongTileSet {
    constructor() {
        this.tiles = [];
        this.tileCounter = 0;
    }

    /**
     * Generate complete set of 144 mahjong tiles
     */
    generateTiles() {
        this.tiles = [];
        this.tileCounter = 0;

        // Generate numbered tiles (4 of each)
        this.generateNumberedTiles();
        
        // Generate wind tiles (4 of each)
        this.generateWindTiles();
        
        // Generate dragon tiles (4 of each, but we skip red dragon)
        this.generateDragonTiles();
        
        // Generate flower tiles (1 of each)
        this.generateFlowerTiles();
        
        // Generate season tiles (1 of each)
        this.generateSeasonTiles();

        return this.tiles;
    }

    /**
     * Generate numbered tiles: Wan (Characters), Tong (Circles), Tiao (Bamboos)
     */
    generateNumberedTiles() {
        const types = [
            { type: 'wan', unicodes: ['🀇', '🀈', '🀉', '🀊', '🀋', '🀌', '🀍', '🀎', '🀏'] },
            { type: 'tong', unicodes: ['🀙', '🀚', '🀛', '🀜', '🀝', '🀞', '🀟', '🀠', '🀡'] },
            { type: 'tiao', unicodes: ['🀐', '🀑', '🀒', '🀓', '🀔', '🀕', '🀖', '🀗', '🀘'] }
        ];

        types.forEach(typeData => {
            for (let value = 1; value <= 9; value++) {
                for (let copy = 0; copy < 4; copy++) {
                    const tile = new MahjongTile(
                        typeData.type,
                        value,
                        typeData.unicodes[value - 1],
                        this.tileCounter++
                    );
                    this.tiles.push(tile);
                }
            }
        });
    }

    /**
     * Generate wind tiles: East, South, West, North
     */
    generateWindTiles() {
        const winds = [
            { value: 'dong', unicode: '🀀' },  // East
            { value: 'nan', unicode: '🀁' },   // South
            { value: 'xi', unicode: '🀂' },    // West
            { value: 'bei', unicode: '🀃' }    // North
        ];

        winds.forEach(wind => {
            for (let copy = 0; copy < 4; copy++) {
                const tile = new MahjongTile(
                    'feng',
                    wind.value,
                    wind.unicode,
                    this.tileCounter++
                );
                this.tiles.push(tile);
            }
        });
    }

    /**
     * Generate dragon tiles: Green Dragon, White Dragon (skip Red Dragon)
     */
    generateDragonTiles() {
        const dragons = [
            { value: 'fa', unicode: '🀅' },    // Green Dragon
            { value: 'bai', unicode: '🀆' }    // White Dragon
        ];

        dragons.forEach(dragon => {
            for (let copy = 0; copy < 4; copy++) {
                const tile = new MahjongTile(
                    'jian',
                    dragon.value,
                    dragon.unicode,
                    this.tileCounter++
                );
                this.tiles.push(tile);
            }
        });
    }

    /**
     * Generate flower tiles: Plum, Orchid, Chrysanthemum, Bamboo
     */
    generateFlowerTiles() {
        const flowers = [
            { value: 'mei', unicode: '🀢' },   // Plum
            { value: 'lan', unicode: '🀣' },   // Orchid
            { value: 'zhu', unicode: '🀤' },   // Chrysanthemum
            { value: 'ju', unicode: '🀥' }     // Bamboo
        ];

        flowers.forEach(flower => {
            const tile = new MahjongTile(
                'hua',
                flower.value,
                flower.unicode,
                this.tileCounter++
            );
            this.tiles.push(tile);
        });
    }

    /**
     * Generate season tiles: Spring, Summer, Autumn, Winter
     */
    generateSeasonTiles() {
        const seasons = [
            { value: 'chun', unicode: '🀦' },  // Spring
            { value: 'xia', unicode: '🀧' },   // Summer
            { value: 'qiu', unicode: '🀨' },   // Autumn
            { value: 'dong', unicode: '🀩' }   // Winter
        ];

        seasons.forEach(season => {
            const tile = new MahjongTile(
                'season',
                season.value,
                season.unicode,
                this.tileCounter++
            );
            this.tiles.push(tile);
        });
    }

    /**
     * Shuffle tiles array
     */
    shuffle() {
        for (let i = this.tiles.length - 1; i > 0; i--) {
            const j = Math.floor(Math.random() * (i + 1));
            [this.tiles[i], this.tiles[j]] = [this.tiles[j], this.tiles[i]];
        }
        return this.tiles;
    }

    /**
     * Get tile by ID
     */
    getTileById(id) {
        return this.tiles.find(tile => tile.id === parseInt(id));
    }

    /**
     * Get all tiles of a specific type
     */
    getTilesByType(type) {
        return this.tiles.filter(tile => tile.type === type && !tile.removed);
    }

    /**
     * Get all selectable tiles
     */
    getSelectableTiles() {
        return this.tiles.filter(tile => tile.selectable && !tile.removed);
    }

    /**
     * Get all selected tiles
     */
    getSelectedTiles() {
        return this.tiles.filter(tile => tile.selected && !tile.removed);
    }

    /**
     * Find matching tiles for a given tile
     */
    findMatches(tile) {
        return this.tiles.filter(otherTile => 
            otherTile.id !== tile.id && 
            !otherTile.removed && 
            otherTile.selectable && 
            tile.canMatch(otherTile)
        );
    }

    /**
     * Check if there are any possible moves
     */
    hasValidMoves() {
        const selectableTiles = this.getSelectableTiles();
        
        for (let i = 0; i < selectableTiles.length; i++) {
            for (let j = i + 1; j < selectableTiles.length; j++) {
                if (selectableTiles[i].canMatch(selectableTiles[j])) {
                    return true;
                }
            }
        }
        
        return false;
    }

    /**
     * Get hint (first available matching pair)
     */
    getHint() {
        const selectableTiles = this.getSelectableTiles();
        
        for (let i = 0; i < selectableTiles.length; i++) {
            for (let j = i + 1; j < selectableTiles.length; j++) {
                if (selectableTiles[i].canMatch(selectableTiles[j])) {
                    return [selectableTiles[i], selectableTiles[j]];
                }
            }
        }
        
        return null;
    }

    /**
     * Get game statistics
     */
    getStats() {
        const total = this.tiles.length;
        const removed = this.tiles.filter(tile => tile.removed).length;
        const remaining = total - removed;
        const selectable = this.getSelectableTiles().length;
        
        return {
            total,
            removed,
            remaining,
            selectable,
            progress: Math.round((removed / total) * 100)
        };
    }

    /**
     * Reset all tiles
     */
    reset() {
        this.tiles.forEach(tile => {
            tile.removed = false;
            tile.selected = false;
            tile.selectable = false;
            tile.x = 0;
            tile.y = 0;
            tile.layer = 0;
            if (tile.element) {
                tile.element.remove();
                tile.element = null;
            }
        });
    }
}

/**
 * Mahjong Layout Engine - Generates different tile layouts
 */
class MahjongLayout {
    constructor() {
        this.tileWidth = 50;
        this.tileHeight = 70;
        this.tileSpacing = 2;
        this.layerOffset = { x: 26, y: 36 };
    }

    /**
     * Generate turtle layout (classic mahjong solitaire)
     */
    generateTurtleLayout() {
        const positions = [];

        // Layer 0 (bottom)
        this.addTurtleLayer0(positions);

        // Layer 1
        this.addTurtleLayer1(positions);

        // Layer 2
        this.addTurtleLayer2(positions);

        // Layer 3
        this.addTurtleLayer3(positions);

        // Layer 4 (top)
        this.addTurtleLayer4(positions);

        return positions;
    }

    addTurtleLayer0(positions) {
        const layer = 0;
        const baseX = 0;
        const baseY = 0;
        const spacing = this.tileSpacing;

        // Main body - more compact layout
        for (let row = 0; row < 8; row++) {
            for (let col = 0; col < 12; col++) {
                if (this.isTurtleBodyPosition(row, col)) {
                    positions.push({
                        x: baseX + col * (this.tileWidth + spacing),
                        y: baseY + row * (this.tileHeight + spacing),
                        layer: layer
                    });
                }
            }
        }

        // Wings - positioned to be clearly separate
        this.addTurtleWings(positions, baseX, baseY, layer);
    }

    addTurtleLayer1(positions) {
        const layer = 1;
        const baseX = this.layerOffset.x;
        const baseY = this.layerOffset.y;

        // Smaller body
        for (let row = 1; row < 7; row++) {
            for (let col = 1; col < 11; col++) {
                if (this.isTurtleLayer1Position(row, col)) {
                    positions.push({
                        x: baseX + col * (this.tileWidth + this.tileSpacing),
                        y: baseY + row * (this.tileHeight + this.tileSpacing),
                        layer: layer
                    });
                }
            }
        }
    }

    addTurtleLayer2(positions) {
        const layer = 2;
        const baseX = this.layerOffset.x * 2;
        const baseY = this.layerOffset.y * 2;

        // Even smaller body
        for (let row = 2; row < 6; row++) {
            for (let col = 2; col < 10; col++) {
                if (this.isTurtleLayer2Position(row, col)) {
                    positions.push({
                        x: baseX + col * (this.tileWidth + this.tileSpacing),
                        y: baseY + row * (this.tileHeight + this.tileSpacing),
                        layer: layer
                    });
                }
            }
        }
    }

    addTurtleLayer3(positions) {
        const layer = 3;
        const baseX = this.layerOffset.x * 3;
        const baseY = this.layerOffset.y * 3;

        // Small center
        for (let row = 3; row < 5; row++) {
            for (let col = 3; col < 9; col++) {
                if (this.isTurtleLayer3Position(row, col)) {
                    positions.push({
                        x: baseX + col * (this.tileWidth + this.tileSpacing),
                        y: baseY + row * (this.tileHeight + this.tileSpacing),
                        layer: layer
                    });
                }
            }
        }
    }

    addTurtleLayer4(positions) {
        const layer = 4;
        const baseX = this.layerOffset.x * 4;
        const baseY = this.layerOffset.y * 4;

        // Top tile
        positions.push({
            x: baseX + 5 * (this.tileWidth + this.tileSpacing),
            y: baseY + 3 * (this.tileHeight + this.tileSpacing),
            layer: layer
        });
    }

    addTurtleWings(positions, baseX, baseY, layer) {
        const spacing = this.tileSpacing;

        // Left wing - clearly separated
        positions.push({
            x: baseX - (this.tileWidth + spacing * 3),
            y: baseY + 3 * (this.tileHeight + spacing),
            layer: layer
        });

        // Right wing - clearly separated
        positions.push({
            x: baseX + 12 * (this.tileWidth + spacing) + spacing * 2,
            y: baseY + 3 * (this.tileHeight + spacing),
            layer: layer
        });
    }

    isTurtleBodyPosition(row, col) {
        // Define the turtle body shape
        const pattern = [
            [0,0,1,1,1,1,1,1,1,1,0,0],
            [0,1,1,1,1,1,1,1,1,1,1,0],
            [1,1,1,1,1,1,1,1,1,1,1,1],
            [1,1,1,1,1,1,1,1,1,1,1,1],
            [1,1,1,1,1,1,1,1,1,1,1,1],
            [1,1,1,1,1,1,1,1,1,1,1,1],
            [0,1,1,1,1,1,1,1,1,1,1,0],
            [0,0,1,1,1,1,1,1,1,1,0,0]
        ];

        return pattern[row] && pattern[row][col] === 1;
    }

    isTurtleLayer1Position(row, col) {
        const pattern = [
            [0,0,0,0,0,0,0,0,0,0,0],
            [0,1,1,1,1,1,1,1,1,1,0],
            [0,1,1,1,1,1,1,1,1,1,0],
            [0,1,1,1,1,1,1,1,1,1,0],
            [0,1,1,1,1,1,1,1,1,1,0],
            [0,1,1,1,1,1,1,1,1,1,0],
            [0,1,1,1,1,1,1,1,1,1,0]
        ];

        return pattern[row - 1] && pattern[row - 1][col - 1] === 1;
    }

    isTurtleLayer2Position(row, col) {
        const pattern = [
            [0,0,0,0,0,0,0,0],
            [0,1,1,1,1,1,1,0],
            [0,1,1,1,1,1,1,0],
            [0,1,1,1,1,1,1,0],
            [0,1,1,1,1,1,1,0]
        ];

        return pattern[row - 2] && pattern[row - 2][col - 2] === 1;
    }

    isTurtleLayer3Position(row, col) {
        const pattern = [
            [0,1,1,1,1,0],
            [0,1,1,1,1,0]
        ];

        return pattern[row - 3] && pattern[row - 3][col - 3] === 1;
    }

    /**
     * Generate pyramid layout
     */
    generatePyramidLayout() {
        const positions = [];
        const baseX = 200;
        const baseY = 150;

        // Layer 0 (base)
        for (let row = 0; row < 6; row++) {
            for (let col = 0; col < 8; col++) {
                positions.push({
                    x: baseX + col * (this.tileWidth + this.tileSpacing),
                    y: baseY + row * (this.tileHeight + this.tileSpacing),
                    layer: 0
                });
            }
        }

        // Layer 1
        for (let row = 1; row < 5; row++) {
            for (let col = 1; col < 7; col++) {
                positions.push({
                    x: baseX + this.layerOffset.x + col * (this.tileWidth + this.tileSpacing),
                    y: baseY + this.layerOffset.y + row * (this.tileHeight + this.tileSpacing),
                    layer: 1
                });
            }
        }

        // Layer 2
        for (let row = 2; row < 4; row++) {
            for (let col = 2; col < 6; col++) {
                positions.push({
                    x: baseX + this.layerOffset.x * 2 + col * (this.tileWidth + this.tileSpacing),
                    y: baseY + this.layerOffset.y * 2 + row * (this.tileHeight + this.tileSpacing),
                    layer: 2
                });
            }
        }

        // Layer 3 (top)
        positions.push({
            x: baseX + this.layerOffset.x * 3 + 3 * (this.tileWidth + this.tileSpacing),
            y: baseY + this.layerOffset.y * 3 + 2 * (this.tileHeight + this.tileSpacing),
            layer: 3
        });

        return positions;
    }

    /**
     * Generate cross layout
     */
    generateCrossLayout() {
        const positions = [];
        const baseX = 150;
        const baseY = 100;

        // Horizontal bar
        for (let col = 0; col < 12; col++) {
            positions.push({
                x: baseX + col * (this.tileWidth + this.tileSpacing),
                y: baseY + 3 * (this.tileHeight + this.tileSpacing),
                layer: 0
            });
        }

        // Vertical bar
        for (let row = 0; row < 8; row++) {
            if (row !== 3) { // Skip center to avoid overlap
                positions.push({
                    x: baseX + 5 * (this.tileWidth + this.tileSpacing),
                    y: baseY + row * (this.tileHeight + this.tileSpacing),
                    layer: 0
                });
            }
        }

        // Add some layered tiles in the center
        for (let layer = 1; layer <= 2; layer++) {
            for (let row = 2; row <= 4; row++) {
                for (let col = 4; col <= 6; col++) {
                    positions.push({
                        x: baseX + this.layerOffset.x * layer + col * (this.tileWidth + this.tileSpacing),
                        y: baseY + this.layerOffset.y * layer + row * (this.tileHeight + this.tileSpacing),
                        layer: layer
                    });
                }
            }
        }

        return positions;
    }

    /**
     * Generate spider layout
     */
    generateSpiderLayout() {
        const positions = [];
        const centerX = 300;
        const centerY = 200;

        // Center body
        for (let row = -1; row <= 1; row++) {
            for (let col = -1; col <= 1; col++) {
                positions.push({
                    x: centerX + col * (this.tileWidth + this.tileSpacing),
                    y: centerY + row * (this.tileHeight + this.tileSpacing),
                    layer: 0
                });
            }
        }

        // Eight legs
        const legDirections = [
            [-1, -1], [-1, 0], [-1, 1],
            [0, -1],           [0, 1],
            [1, -1],  [1, 0],  [1, 1]
        ];

        legDirections.forEach(([dx, dy]) => {
            for (let i = 2; i <= 4; i++) {
                positions.push({
                    x: centerX + dx * i * (this.tileWidth + this.tileSpacing),
                    y: centerY + dy * i * (this.tileHeight + this.tileSpacing),
                    layer: 0
                });
            }
        });

        // Add some layered tiles
        positions.push({
            x: centerX,
            y: centerY,
            layer: 1
        });

        return positions;
    }

    /**
     * Get layout by name
     */
    getLayout(layoutName) {
        switch (layoutName) {
            case 'turtle':
                return this.generateTurtleLayout();
            case 'pyramid':
                return this.generatePyramidLayout();
            case 'cross':
                return this.generateCrossLayout();
            case 'spider':
                return this.generateSpiderLayout();
            default:
                return this.generateTurtleLayout();
        }
    }
}

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { MahjongTile, MahjongTileSet, MahjongLayout };
}
